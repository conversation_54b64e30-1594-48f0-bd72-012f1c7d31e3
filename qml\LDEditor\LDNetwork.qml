import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Shapes 1.2
import "qrc:/qml/gv"

//LD网络
Rectangle {
    id: control
    // 主控件
    property var mainControl
    // 配置
    property var config: LDEditorConfiger
    // 单个网络组件的数据
    property var networkData
    // 默认宽度
    property int defaultWidth : 700
    // 默认高度
    property int defaultHeight : 200
    // 包含or块元件的每组y轴中最大的x轴
    property var includeOrMaxXPosList: findMaxXInYGroup(true)
    // 当前网络中的链接
    property var currentNetWorkConnections
    // 当前网络中选中的坐标块
    property var currentSelectPositionBlock
    // 当前网络中所有的块元件
    property var components: []
    // 当前网络是否剪切
    property bool isShear: false

    width: Math.max(defaultWidth, txt_sortnumber.width + column.implicitWidth + 1)
    height: Math.max(defaultHeight, column.implicitHeight + (column.children.length - 1) * 4)

    border.width: isShear ? 0 : config.defaultLineWidth
    border.color: Qt.rgba(128/255, 128/255, 128/255, 1)
    color: isShear ? "#dddddd" : "transparent"

    //边线
    Shape {
        anchors.fill: parent
        visible: isShear
        ShapePath {
            strokeWidth: config.defaultLineWidth
            strokeColor: "#a9a9a9"
            strokeStyle: ShapePath.DashLine
            fillColor: "transparent"
            startX: 0
            startY: 0
            PathLine {
                x: control.width
                y: 0
            }
            PathLine {
                x: control.width
                y: control.height
            }
            PathLine {
                x: 0
                y: control.height
            }
            PathLine {
                x: 0
                y: 0
            }
        }
    }

    // 检查是否被剪切
    function checkShear()
    {
        // 获取不会更新粘贴次数的剪贴板内容
        const snapshot = shortcutController.getClipbooardContentWithoutCount(fileType, deviceName, "NETWORK")
        
        if(snapshot && snapshot.type === "shear" && snapshot.pasteCount === 0)
        {
            // 整个编辑器中网络数据
            const netWorks = snapshot.data.list

            for(let nIndex = 0; nIndex < netWorks.length; nIndex++)
            {
                if(netWorks[nIndex].Number === networkData.Number)
                {
                    return true
                }
            }
        }

        return false
    }
    
    onNetworkDataChanged: {
        components = networkData.components
        // 过滤后的块元件链接
        const filterConnections = []
        // 块元件id
        const componentIds = getCurrentNetWorkComponentIds()
        // 块元件链接
        const connections = networkData.connections
        
        for(let connIndex = 0; connIndex < connections.length; connIndex++)
        {
            // 输出端块元件号
            const sourceNumber = connections[connIndex].SourceComponentNumber
            // 输入端块元件号
            const targetNumber = connections[connIndex].TargetComponentNumber
            
            // 校验块元件链接的id是否存在
            if(componentIds.includes(sourceNumber) && componentIds.includes(targetNumber))
            {
                filterConnections.push(connections[connIndex])
            }
        }
        
        currentNetWorkConnections = filterConnections
        block_rect.updateSize()
        isShear = checkShear()
    }

    Row {
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.bottom: parent.bottom
        // 网络序号
        Rectangle {
            id: txt_sortnumber
            height: parent.height
            width: config.defaultNetworkNumberWidth + 10
            border.color: Qt.rgba(128/255, 128/255, 128/255, 1)
            border.width: config.defaultLineWidth
            Rectangle {
                height: parent.height
                width: config.defaultNetworkNumberWidth
                border.color: Qt.rgba(64/255, 64/255, 64/255, 1)
                border.width: config.defaultLineWidth
                color: number_txt.focus || blank_space.focus ? "#6495ed" : config.defaultNetworkNumberBackgroundColor
                TextInput {
                    id: number_txt
                    anchors.fill: parent
                    readOnly: true
                    text: networkData.Number.toString().padStart(4, "0")
                    font.pixelSize: config.networkNumberFontPixelSize
                    color: config.defaultNetworkNumberColor
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    TapHandler {
                        acceptedButtons: Qt.LeftButton | Qt.RightButton
                        onTapped: {
                            activeFocusItem = "NETWORK"
                            currentSelectBlockType = ""
                            currentSelectBolckData = {}
                            parent.forceActiveFocus()
                            mainControl.startBlockNumber = getStartBlockNumber()
                            mainControl.netWorkNumber = networkData.Number
                            let positionInRoot = mapToItem(mainControl, eventPoint.position.x, eventPoint.position.y)
                            if(eventPoint.event.button === Qt.RightButton)
                            {
                                mainControl.showMenu(positionInRoot.x, positionInRoot.y, "NETWORK")
                            }
                        }
                    }
                }
            }
        }

        Column {
            id: column
            width: parent.width - txt_sortnumber.width - spacing
            height: parent.height
            spacing: 5
            anchors.top: parent.top
            anchors.topMargin: config.defaultMargin
            anchors.bottom: parent.bottom
            Rectangle {
                id: txt_label_rect
                width: parent.width
                height: config.defaultDescHeight
                color: txt_label.focus ? "#6495ed" : "transparent"
                TextInput {
                    id: txt_label
                    text: networkData.Label
                    validator: RegExpValidator {
                        regExp: GlobalVariable.varname_regular
                    }
                    width: parent.width
                    font.pixelSize: config.fontPixelSize
                    anchors.verticalCenter: parent.verticalCenter
                    color: "black"
                    selectByMouse: true
                    selectionColor: config.defaultSelectionColor
                    selectedTextColor: config.defaultSelectedTextColor
                    onFocusChanged: {
                        if(!focus)
                        {
                            const regex = GlobalVariable.varname_regular

                            if(txt_label.text !== networkData.Label && regex.test(txt_label.text))
                            {
                                ldManage.modifyNetworkLabel(fileKey, networkData.Number, txt_label.text)
                                getNetWork()
                                updateFileFlag(fileKey, true)
                            }
                            else
                            {
                                txt_label.text = networkData.Label
                            }
                        }
                    }
                }
            }

            Rectangle {
                id: txt_desc_rect
                width: parent.width
                height: config.defaultDescHeight
                color: txt_desc.focus ? "#6495ed" : "#7f7f7f"
                TextInput {
                    id: txt_desc
                    text: networkData.Comment
                    width: parent.width
                    font.pixelSize: config.fontPixelSize
                    anchors.verticalCenter: parent.verticalCenter
                    color: focus ? "black" : "white"
                    selectByMouse: true
                    selectionColor: config.defaultSelectionColor
                    selectedTextColor: config.defaultSelectedTextColor
                    onEditingFinished: {
                        if(txt_desc.text !== networkData.Comment)
                        {
                            ldManage.modifyNetworkComment(fileKey, networkData.Number, txt_desc.text)
                            getNetWork()
                            updateFileFlag(fileKey, true)
                        }
                    }
                }
                Text {
                    text: "注释..."
                    font.pixelSize: config.fontPixelSize
                    color: "#ffffff"
                    visible: !txt_desc.text && !txt_desc.focus
                    z: 0
                }
            }

            Rectangle {
                id: block_rect
                width: calculateWidth()
                height: calculateHeight()
                color: "transparent"
                Repeater {
                    id: block_repe
                    model: networkData.components.length
                    LDBlock {
                        mainControl: control.mainControl
                        netWorkControl: control
                        blockData: networkData.components[index]
                        startBlockNumber: getStartBlockNumber()
                        netWorkNumber: networkData.Number
                        Component.onCompleted: {
                            calculatePositionBlockPoints.connect(ld_position_blocks.calculatePositionBlockPoints)
                        }
                    }
                }

                function calculateWidth()
                {
                    var maxWidth = 0
                    for (var i = 0; i < block_repe.count; i++) 
                    {
                        var item = block_repe.itemAt(i)
                        if (item)
                        {
                            maxWidth = Math.max(maxWidth, item.x + item.width)
                        }
                    }
                    return maxWidth + 1
                }

                function calculateHeight()
                {
                    var maxHeight = 0
                    for (var i = 0; i < block_repe.count; i++) 
                    {
                        var item = block_repe.itemAt(i)
                        if (item)
                        {
                            maxHeight = Math.max(maxHeight, item.y + item.height)
                        }
                    }
                    return maxHeight
                }

                function getItems()
                {
                    let blockItems = []

                    for (var i = 0; i < block_repe.count; i++) 
                    {
                        blockItems.push(block_repe.itemAt(i))
                    }
                    ld_connections.blockItems = blockItems
                    ld_position_blocks.blockItems = blockItems
                }

                function updateSize()
                {
                    Qt.callLater(function() {
                        block_rect.width = calculateWidth()
                        block_rect.height = calculateHeight()
                        getItems()
                    })
                }
            }
        }
    }

    // 块元件链接
    LDConnections {
        id: ld_connections
        width: control.width
        height: control.height
        connectionStartX: txt_sortnumber.width
        connectionStartY: block_rect.y
        connections: control.currentNetWorkConnections
        mainControl: control.mainControl
    }

    // 坐标块
    LDPositionBlocks {
        id: ld_position_blocks
        anchors.fill: parent
        positionBlockStartX: txt_sortnumber.width
        positionBlockStartY: block_rect.y
        mainControl: control.mainControl
        netWorkControl: control
        visible: mainControl.netWorkNumber === networkData.Number && mainControl.isShowPositionBlocks
    }

    TextInput {
        id: blank_space
        anchors.fill: parent
        z: -1
        readOnly: true
        font.pixelSize: config.networkNumberFontPixelSize
        color: config.defaultNetworkNumberColor
        horizontalAlignment: Text.AlignHCenter
        verticalAlignment: Text.AlignVCenter

        TapHandler {
            acceptedButtons: Qt.LeftButton | Qt.RightButton
            onTapped: {
                activeFocusItem = "NETWORK"
                currentSelectBlockType = ""
                currentSelectBolckData = {}
                parent.forceActiveFocus()
                mainControl.startBlockNumber = getStartBlockNumber()
                mainControl.netWorkNumber = networkData.Number
                let positionInRoot = mapToItem(mainControl, eventPoint.position.x, eventPoint.position.y)
                if(eventPoint.event.button === Qt.RightButton)
                {
                    mainControl.showMenu(positionInRoot.x, positionInRoot.y, "NETWORK")
                }
            }
        }
    }

    // 获取当前网络中start块元件对应的序号
    function getStartBlockNumber()
    {
        // 遍历所有的块元件,找到start块元件
        for(let cIndex = 0; cIndex < components.length; cIndex++)
        {
            // 校验是否是start块元件
            if(getBlockType(components[cIndex]) === "start")
            {
                return components[cIndex].Number
            }
        }

        return -1
    }

    // 获取当前网络中所有块元件的id
    function getCurrentNetWorkComponentIds()
    {
        const componentIds = []
        
        for(let cIndex = 0; cIndex < components.length; cIndex++)
        {
            componentIds.push(components[cIndex].Number)
        }
        
        return componentIds
    }

    // 获取当前网络中每组y轴中最大的x轴
    function findMaxXInYGroup(isIncludeOr)
    {
        const maxXPosList = {}

        for(let cIndex = 0; cIndex < components.length; cIndex++)
        {
            const component = components[cIndex]

            if(isIncludeOr && getBlockType(component) === "or")
            {
                continue
            }

            const x = component.XPos
            const y = component.YPos

            if(maxXPosList[y] === undefined)
            {
                maxXPosList[y] = x
            }
            else
            {
                maxXPosList[y] = Math.max(maxXPosList[y], x)
            }
        }

        return maxXPosList
    }

    // 校验指定坐标是否存在扩展块元件
    function isExistExpandBlock(xPos, yPos, isXSame, isYSame)
    {
        for(let cIndex = 0; cIndex < components.length; cIndex++)
        {
            const component = components[cIndex]
            
            if(isXSame && !isYSame)
            {
                if(component.XPos === xPos && 
                   component.YPos !== yPos && 
                   isFuncOrFuncBlock(component)
                  )
                {
                    return true
                }
            }
            else if(!isXSame && !isYSame)
            {
                if(component.XPos <= xPos && 
                   (component.YPos !== yPos || 
                    component.YPos === yPos) && 
                   isFuncOrFuncBlock(component)
                  )
                {
                    return true
                }
            }
            else if(isXSame && isYSame)
            {
                if(component.XPos === xPos && 
                   component.YPos === yPos && 
                   isFuncOrFuncBlock(component)
                  )
                {
                    return true
                }
            }
        }

        return false
    }
}
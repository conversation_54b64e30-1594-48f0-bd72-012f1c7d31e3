import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Shapes 1.2
import "qrc:/qml/gv"

// LD块
Rectangle {
    id: control
    color: "transparent"
    width: loader.width
    height: loader.height + config.defaultDescHeight

    signal calculatePositionBlockPoints(var mouseX, var mouseY)
    // 主控件
    property var mainControl
    // 当前所在的网络控件
    property var netWorkControl
    // 块数据
    property var blockData
    // 配置
    property var config: LDEditorConfiger
    // 记录当前选中的网络中对应的start块元件序号
    property int startBlockNumber
    // 当前所在的网络号
    property int netWorkNumber
    // 当前块元件是否剪切
    property bool isShear: false
    
    onBlockDataChanged: {
        // 块元件数据变更之后需要重新计算块元件所在的x和y
        calculatePosition()
        isShear = checkShear()
        if(loader.item)
        {
            loader.source = getComponent()
            loader.item.blockData = control.blockData
            loader.item.mainControl = control.mainControl
            loader.item.netWorkControl = control.netWorkControl
            loader.item.startBlockNumber = control.startBlockNumber
            loader.item.netWorkNumber = control.netWorkNumber
        }
    }

    // 检查是否被剪切
    function checkShear()
    {
        // 获取不会更新粘贴次数的剪贴板内容
        const snapshot = shortcutController.getClipbooardContentWithoutCount(fileType, deviceName, "BLOCK")
        
        if(snapshot && snapshot.type === "shear" && snapshot.pasteCount === 0)
        {
            // 块元件数据
            const blocks = snapshot.data.list
            
            for(let bIndex = 0; bIndex < blocks.length; bIndex++)
            {
                if(blocks[bIndex].Name === blockData.Name)
                {
                    return true
                }
            }
        }

        return false
    }

    Rectangle {
        width: parent.width
        height: parent.height
        color: "#dddddd"
        visible: isShear
        //边线
        Shape {
            anchors.fill: parent
            ShapePath {
                strokeWidth: config.defaultLineWidth
                strokeColor: "#a9a9a9"
                strokeStyle: ShapePath.DashLine
                fillColor: "transparent"
                startX: 0
                startY: 0
                PathLine {
                    x: control.width
                    y: 0
                }
                PathLine {
                    x: control.width
                    y: control.height
                }
                PathLine {
                    x: 0
                    y: control.height
                }
                PathLine {
                    x: 0
                    y: 0
                }
            }
        }
    }

    // 元件块
    Column {
        anchors.fill: parent

        // 块元件文本
        Rectangle {
            width: isShow() ? parent.width : 0
            height: config.defaultDescHeight
            anchors.left: parent.left
            anchors.leftMargin: config.defaultLineWidth
            anchors.right: parent.right
            anchors.rightMargin: config.defaultLineWidth
            color: isShear ? "transparent" : (variable_txt.focus ? "#6495ed" : "transparent")
            TextInput {
                id: variable_txt
                visible: isShow() && blockData.Number !== startBlockNumber
                anchors.centerIn: parent
                width: parent.width
                font.pixelSize: config.fontPixelSize
                horizontalAlignment: Text.AlignHCenter
                // text: {
                //     const variableData = getVariableData()
                //     return Object.keys(variableData).length === 0 ? "" : variableData.AuxContent
                // }
                text: isTest ? blockData.Number : blockData.TaskOrderNumber
                enabled: !isTest
                color: "black"
                clip: true
                selectByMouse: true
                selectionColor: config.defaultSelectionColor
                selectedTextColor: config.defaultSelectedTextColor

                MouseArea {
                    anchors.fill: parent
                    acceptedButtons: Qt.LeftButton
                    cursorShape: Qt.OpenHandCursor
                    onClicked: {
                        if (mouse.button === Qt.LeftButton || mouse.button === Qt.RightButton)
                        {
                            const variableData = getVariableData()
                            const leftOrRight = isFlowEndBlock(getBlockType(blockData)) ? 1 : 0

                            //显示变量弹窗 含全局变量 局部变量 IO变量 常量?
                            var newCData = {
                                "NetworkNumber": netWorkNumber,
                                "VarCompNumber": variableData.Number,
                                "DataType": variableData.ParentPinDataType,
                                "LeftOrRight": leftOrRight,
                                "AuxContent": variableData.AuxContent
                            }
                            showSelectVariableDialog(newCData)
                        }
                    }
                }
            }
        }

        Loader {
            id: loader
            source: getComponent()
            onLoaded: {
                loader.item.blockData = control.blockData
                loader.item.mainControl = control.mainControl
                loader.item.netWorkControl = control.netWorkControl
                loader.item.startBlockNumber = control.startBlockNumber
                loader.item.netWorkNumber = control.netWorkNumber
            }
        }
    }

    function getComponent()
    {
        switch(getBlockType(blockData))
        {
            case "or":
                return "LD_OR.qml"
            case "start":
            case "conatct":
            case "variable":
                return "LD_CONTACT.qml"
            case "jump":
            case "return":
            case "set0":
            case "set1":
            case "coil":
                return "LD_COIL.qml"
            case "func":
            case "fb":
            case "advance":
                return "LD_FUN.qml"
        }
    }

    function isShow()
    {
        switch(getBlockType(blockData))
        {
            case "start":
            case "or":
                return false
            case "func":
            case "fb":
            case "advance":
            case "return":
            case "set0":
            case "set1":
            case "coil":
            case "conatct":
            case "jump":
                return true
        }

        return false
    }

    // 计算当前块元件所在的位置
    function calculatePosition()
    {
        // 是否已经计算过扩展块元件的宽度
        let isCalculateExpandBlockWidth = false
        let cIndex = 0
        let xCount = 0
        // 最终计算出来的x和y坐标
        let xPosition = 0
        let yPosition = 0
        // 当前块元件所在的x和y坐标的索引
        const xPos = blockData.XPos
        const yPos = blockData.YPos
        // 用于记录每一行中最大的高度的块元件
        const yComponents = []
        // 当前块元件之前（在同一行上 x 方向）块元件
        const xComponents = []
        var component

        // 是否存在扩展块元件
        if(isExistExpandBlock(xPos, yPos, false, false))
        {
            // 小于等于当前x轴的扩展块元件的数据
            let expandBlockCount = 0
            // 相同的x轴是否有扩展块元件
            let isExpandBlock = false
            // 扩展块元件
            let expandBlocks = {}
            // 自身是否是扩展块元件
            const selfIsExpandBlock = isExistExpandBlock(xPos, yPos, true, true)

            for(cIndex = 0; cIndex < components.length; cIndex++)
            {
                component = components[cIndex]
                
                if(component.XPos <= xPos && 
                   isFuncOrFuncBlock(component) &&
                   !expandBlocks[component.XPos]
                  )
                {
                    expandBlocks[component.XPos] = true
                    expandBlockCount++
                }

                // 校验相同的x轴上是否有扩展块元件
                // 相同的x轴上有扩展块元件那就需要就当前的块元件居中
                if(component.XPos === xPos && 
                   component.YPos !== yPos && 
                   isFuncOrFuncBlock(component)
                  )
                {
                    isExpandBlock = true
                }
            }

            // console.log("xPos:", xPos)
            // console.log("yPos:", yPos)
            // console.log("expandBlockCount:", expandBlockCount)
            if(isExpandBlock && !selfIsExpandBlock)
            {
                xPosition += config.defaultBlockWidth * config.cellWidth
                expandBlockCount--
            }

            if(selfIsExpandBlock)
            {
                expandBlockCount--
            }

            // console.log("xPosition:", xPosition)
            
            if(expandBlockCount > 0)
            {
                xPosition += (config.defaultBlockWidth * config.cellWidth * 2) * expandBlockCount
            }

            // console.log("xPosition:", xPosition)

            // 已经计算过扩展块元件宽度
            isCalculateExpandBlockWidth = true
        }
        
        for(cIndex = 0; cIndex < components.length; cIndex++)
        {
            component = components[cIndex]
            
            // 处理 x 坐标：统计在当前块元件左侧（XPos 更小）且在同一行（YPos 相等）的块元件
            if(component.XPos < xPos && component.YPos === yPos && (component.XPos !== 0 && component.YPos !== 0))
            {
                // 累加其宽度用于计算当前块的 x 坐标
                xPosition += config.defaultBlockWidth * config.cellWidth // component.Width * config.cellWidth
                xComponents.push(component)
            }
            
            // 处理 y 坐标：查找当前块元件上方的每一行中最高的块元件
            if(component.YPos >= 0 && component.YPos < yPos)
            {
                if(!yComponents[component.YPos])
                {
                    yComponents[component.YPos] = component
                    continue
                }

                if(getRealityHeight(yComponents[component.YPos]) < getRealityHeight(component))
                {
                    // 如果已有块元件高度小于当前块元件，则替换为更高的那个
                    yComponents[component.YPos] = component
                }
            }
        }

        // console.log("xPosition:", xPosition)
        xCount = xComponents.length
        // 如果统计的块元件数量少于当前块左边应有的位置数，说明中间有空白
        if(xCount < xPos)
        {
            // 块元件是否存在
            let isExist = false
            // 用默认宽度来填补空白位置
            // 除了第一行有0,0坐标,其余行是没有0,0的
            // 所以索引需要从1开始
            for(let xcIndex = 1; xcIndex < xPos; xcIndex++)
            {
                isExist = false

                for(let xComIndex = 0; xComIndex < xComponents.length; xComIndex++)
                {
                    // 校验该坐标上是否存在实际的块元件
                    if(xComponents[xComIndex].XPos === xcIndex && 
                       xComponents[xComIndex].YPos === yPos
                      )
                    {
                        isExist = true

                        // 当前块元件存在则校验该y轴上是否存在扩展块元件
                        if(isExistExpandBlock(xComponents[xComIndex].XPos, xComponents[xComIndex].YPos, true, false) && !isCalculateExpandBlockWidth)
                        {
                            // 如果存在,并且相同的y轴存在扩展块元件,那则需要*2
                            // 不*3,是因为当前的块元件不是空白块元件,而是一个实际存在的块元件
                            // 实际存在的块元件在上面处理x坐标的时候已经加上了这个块元件的大小
                            xPosition += config.defaultBlockWidth * config.cellWidth * 2
                        }

                        break
                    }
                }

                if(!isExist)
                {
                    xPosition += config.defaultBlockWidth * config.cellWidth
                }
            }
        }

        // console.log("xPosition:", xPosition)
        // console.log("======================")
        // 累加每一行（在当前块上方）的最大高度，以计算 y 坐标
        for(let yIndex = 0; yIndex < yComponents.length; yIndex++)
        {
            yPosition += getRealityHeight(yComponents[yIndex])
        }
        
        control.x = xPosition
        control.y = yPosition + yComponents.length * config.defaultDescHeight
    }

    // 获取真实高度
    function getRealityHeight(component)
    {
        let height = 0

        if(!component)
        {
            return height
        }

        if(isFuncOrFuncBlock(component))
        {
            height = (component.Height - 1) * pinHeight + funBlockTextHeight + posTextHeight
        }
        else
        {
            height = component.Height * config.cellHeight
        }

        return height
    }

    // 获取变量元件数据
    function getVariableData()
    {
        let comp = {}
        // 当前网络中的变量元件
        const variableBlocks = networkData.variableBlocks
        
        for(let comIndex = 0; comIndex < variableBlocks.length; comIndex++)
        {
            const component = variableBlocks[comIndex]

            if(component.ParentNumber === blockData.Number)
            {
                comp = component
                break
            }
        }
        
        return comp
    }
}
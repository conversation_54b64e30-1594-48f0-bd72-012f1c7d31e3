import QtQuick 2.15
import QtQuick.Controls 2.15

Item {
    // 主控件
    property var mainControl
    // 当前所在的网络组件
    property var netWorkControl
    // 配置
    property var config: LDEditorConfiger
    // 存放每个块元件的所有属性
    property var blockItems
    // 坐标块x起点坐标
    property int positionBlockStartX
    // 坐标块y起点坐标
    property int positionBlockStartY
    // 坐标轴
    property var points: []
    // 坐标块的宽度
    property int blockWidth: 12
    // 坐标块的高度
    property int blockHeight: 12

    onBlockItemsChanged: {
        netWorkControl.currentSelectPositionBlock = {}
    }

    // 计算坐标块的坐标点
    function calculatePositionBlockPoints(mouseX = 0, mouseY = 0)
    {
        const newPoints = []
        let startX = 0
        let startY = 0
        let endX = 0
        let endY = 0
        // 当前选中的块元件类型
        const selectBlockType = getBlockType(mainControl.currentSelectBolckData)
        const selectBlockXPos = mainControl.currentSelectBolckData.XPos
        const selectBlockYPos = mainControl.currentSelectBolckData.YPos
        
        for(let bItemIndex = 0; bItemIndex < blockItems.length; bItemIndex++)
        {
            // 块元件所在的组件
            const blockItem = blockItems[bItemIndex]
            // 块元件数据
            const blockData = blockItem.blockData
            // 起点块元件的中心所在高度
            const centreHeight = (blockData.Height * config.cellHeight - config.defaultLineWidth) / 2 + config.defaultDescHeight - config.defaultLineWidth / 2
            // 选中的块元件是没有坐标块的
            if(mainControl.currentSelectBolckData && mainControl.currentSelectBolckData.Number === blockData.Number)
            {
                startX = blockItem.x + positionBlockStartX
                startY = blockItem.y + positionBlockStartY
                endX = startX + blockItem.width
                endY = startY + blockItem.height
                continue
            }

            const XPos = blockData.XPos
            const YPos = blockData.YPos
            let leftOrRight = -1
            
            // 校验拖动的块元件是否是当前块元件的左右
            // 如果是在当前块元件的左右则不显示拖动点
            // 减少额外操作
            if(selectBlockYPos === YPos && (selectBlockXPos + 1 === XPos))
            {
                leftOrRight = 0
            }
            else if(selectBlockYPos === YPos && (selectBlockXPos - 1 === XPos))
            {
                leftOrRight = 2
            }

            // "position": "0" 表示坐标块在块元件的左边
            // "position": "1" 表示块元件本体
            // "position": "2" 表示坐标块在块元件的右边
            // "position": "3" 表示坐标块在块元件的上边
            // "position": "4" 表示坐标块在块元件的下边
            switch(getBlockType(blockData))
            {
                case "start":
                    // start块元件
                    // start块元件只有在选中了conatct块元件的时候才有坐标块
                    // if(selectBlockType === "conatct")
                    // {
                    //     newPoints.push({"x": blockItem.x + positionBlockStartX - blockWidth / 2 - config.defaultLineWidth / 2, 
                    //                     "y": blockItem.y + positionBlockStartY + centreHeight,
                    //                     "color": "#0000ff", "number": blockData.Number, "position": "2"})
                    // }
                    break
                case "or":
                    // or块元件
                    // newPoints.push({"x": blockItem.x + positionBlockStartX - blockWidth / 2 - config.defaultLineWidth / 2, 
                    //                 "y": blockItem.y + positionBlockStartY + centreHeight,
                    //                 "color": "#0000ff", "number": blockData.Number, "position": "2"})
                    break
                case "conatct":
                    // conatct块元件
                    if(selectBlockType === "conatct")
                    {
                        if(leftOrRight === 0)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockItem.width - blockWidth * 2, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                        }
                        else if(leftOrRight === 2)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                        else
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockItem.width - blockWidth * 2, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                    }
                    else if(selectBlockType === "coil" ||
                            selectBlockType === "jump" ||
                            selectBlockType === "return" ||
                            selectBlockType === "set0" ||
                            selectBlockType === "set1")
                    {
                        // todo
                    }
                    // function、functionBlock、advance
                    else if(selectBlockType === "func" ||
                            selectBlockType === "fb" ||
                            selectBlockType === "advance")
                    {
                        if(leftOrRight === 0)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockItem.width - blockWidth * 2, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                        }
                        else if(leftOrRight === 2)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                        else
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockItem.width - blockWidth * 2, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})       
                        }
                    }
                    break
                case "func":
                case "fb":
                case "advance":
                    // functuin、functionBlock、advance
                    if(selectBlockType === "conatct")
                    {
                        if(leftOrRight === 0)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) * 2 + (blockWidth) - 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                        }
                        else if(leftOrRight === 2)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) - (blockWidth * 2) + 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                        else
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) * 2 + (blockWidth) - 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) - (blockWidth * 2) + 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                    }
                    else if(selectBlockType === "coil" ||
                            selectBlockType === "jump" ||
                            selectBlockType === "return" ||
                            selectBlockType === "set0" ||
                            selectBlockType === "set1")
                    {
                        // todo
                    }
                    else if(selectBlockType === "func" ||
                            selectBlockType === "fb" ||
                            selectBlockType === "advance")
                    {
                        if(leftOrRight === 0)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) * 2 + (blockWidth) - 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                        }
                        else if(leftOrRight === 2)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) - (blockWidth * 2) + 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                        else
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) - (blockWidth * 2) + 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"}) 
                            newPoints.push({"x": blockItem.x + positionBlockStartX + (blockItem.width / 3) * 2 + (blockWidth) - 5, 
                                        "y": blockItem.y + positionBlockStartY + funBlockTextHeight + posTextHeight + 
                                                config.defaultDescHeight + cellCentreHeight - config.defaultLineWidth,
                                        "color": "#0000ff", "number": blockData.Number, "position": "2"})
                        }
                    }
                    break
                case "coil":
                case "jump":
                case "return":
                case "set0":
                case "set1":
                    // 线圈
                    // 校验选中的块元件是否是conatct块元件
                    if(selectBlockType === "conatct")
                    {
                        if(leftOrRight === -1)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                    }
                    // 校验选中的块元件是否是线圈、跳转、返回元件
                    else if(selectBlockType === "coil" ||
                            selectBlockType === "jump" ||
                            selectBlockType === "return" ||
                            selectBlockType === "set0" ||
                            selectBlockType === "set1")
                    {
                        // newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                        //                 "y": blockItem.y + positionBlockStartY + centreHeight - blockHeight / 2,
                        //                 "color": "#0000ff", "number": blockData.Number, "position": "3"})
                        // newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                        //                 "y": blockItem.y + positionBlockStartY + centreHeight + blockHeight / 2,
                        //                 "color": "#0000ff", "number": blockData.Number, "position": "4"})
                    }
                    // 校验选中的块元件是否是function、functionBlock、advance
                    else if(selectBlockType === "func" ||
                            selectBlockType === "fb" ||
                            selectBlockType === "advance")
                    {
                        if(leftOrRight === -1)
                        {
                            newPoints.push({"x": blockItem.x + positionBlockStartX + blockWidth, 
                                        "y": blockItem.y + positionBlockStartY + centreHeight,
                                        "color": "#0000ff", "number": blockData.Number, "position": "0"})
                        }
                    }
                    break
            }
        }

        points = updateClosestPointColor(newPoints, mouseX, mouseY, startX, startY, endX, endY)
        // console.log("points:", JSON.stringify(points))
    }

    // 获取最近的坐标块并修改最近的坐标块的颜色
    function updateClosestPointColor(points, mouseX, mouseY, startX, startY, endX, endY)
    {
        let closestPoint = null
        let minDistance = Infinity

        // 如果鼠标超出了范围，则不计算最近的坐标块
        if(mouseX < 0 || mouseY < 0)
        {
            return points
        }

        // 如果鼠标在start块元件的范围内，则不计算最近的坐标块
        if(mouseX >= (startX - blockWidth) && mouseY >= startY && mouseX <= (endX - blockWidth) && mouseY <= endY)
        {
            return points
        }
        
        for (let pIndex = 0; pIndex < points.length; pIndex++)
        {
            const point = points[pIndex]

            // 坐标块必须在鼠标附近
            if (Math.abs(mouseX - point.x) >= 50 || Math.abs(mouseY - point.y) >= 50) 
            {
                continue
            }

            const dx = point.x - mouseX
            const dy = point.y - mouseY
            const distance = dx * dx + dy * dy

            if (distance < minDistance)
            {
                minDistance = distance
                closestPoint = point
            }
        }

        if(closestPoint !== null)
        {
            closestPoint.color = "#ff0000"
            netWorkControl.currentSelectPositionBlock = closestPoint
        }
        else
        {
            netWorkControl.currentSelectPositionBlock = {}
        }

        return points
    }

    Repeater {
        model: points.length
        Item {
            anchors.fill: parent
            Rectangle {
                width: blockWidth
                height: blockHeight
                color: points[index].color
                x: points[index].x
                y: points[index].y
            }
        }
    }
}
import QtQuick 2.15
import QtQuick.Controls 2.15

Item {

    function operationContent(operateType)
    {
        const content = {
            type: operateType,
            from: fileKey,
            deviceName: deviceName
        }

        const data = {
            source: "content",
            type: activeFocusItem,
            list: []
        }

        let clipboardData
        
        if(activeFocusItem === "NETWORK")
        {
            if(operateType === "copy")
            {
                clipboardData = ldManage.copyNetwork(fileKey, netWorkNumber)
            }
            else if(operateType === "shear")
            {
                clipboardData = ldManage.cutNetwork(fileKey, netWorkNumber)
            }
        }
        else if(activeFocusItem === "BLOCK")
        {
            if(operateType === "copy")
            {
                clipboardData = ldManage.copyComponent(fileKey, currentSelectBolckData.Number)
            }
            else if(operateType === "shear")
            {
                clipboardData = ldManage.cutComponent(fileKey, currentSelectBolckData.Number)
            }
        }

        data.list.push(clipboardData)
        content.data = data
        return content
    }

    // 复制
    function copy()
    {
        return operationContent("copy")
    }

    // 剪切
    function shear()
    {
        return operationContent("shear")
    }

    function paste_(location, handler)
    {
        const content = handler(fileType, deviceName, activeFocusItem)
        
        if(!content || Object.keys(content).length === 0)
        {
            // 内容为空,可能是剪切内容已经被粘贴过,不允许多次粘贴
            return
        }

        // 内容类型
        const contentType = content.data.type
        // 内容集合
        const contentList = content.data.list
        // 文件路径
        const contentFileKey = content.from
        // 是否是剪切
        const isShear = content.type === "shear"
        // 执行结果
        let result = true

        // 如果是剪切的内容,需要在粘贴之前将剪切的内容相关的数据删除掉
        // if(isShear)
        // {
        //     // 删除剪切的内容
        //     contentList.map(item => {
        //         if(contentType === "NETWORK")
        //         {
        //             // result = ldManage.deleteNetwork(contentFileKey, item.Number)
        //         }
        //         else if(contentType === "BLOCK")
        //         {
        //             // result = ldManage.deleteComponent(contentFileKey, item.Number)
        //         }
        //     })
        // }

        // 粘贴内容
        if(result)
        {
            if(activeFocusItem === "NETWORK")
            {
                contentList.map(item => {
                    ldManage.pasteNetwork(fileKey, netWorkNumber, item, location) 
                })
               
            }
            else if(activeFocusItem === "BLOCK")
            {
                contentList.map(item => {
                    paste(location, item)
                })
            }

            ldManage.fixLDFileConnections(fileKey)
        }
    }
    
    // 右粘贴
    function rightPaste(handler)
    {
        if(!activeFocusItem)
        {
            // 未选中网络或块元件
            return
        }

        const shouldHandlePaste = activeFocusItem === "NETWORK" || 
                                 (activeFocusItem === "BLOCK" && isCanPaste(1))

        // 校验当前位置是否能粘贴
        if (shouldHandlePaste)
        {
            paste_(1, handler)
        }
    }

    // 左粘贴
    function leftPaste(handler)
    {
        if(!activeFocusItem)
        {
            // 未选中块元件
            return
        }

        if(activeFocusItem === "BLOCK" && isCanPaste(0))
        {
            paste_(0, handler)
        }
    }

    // 下粘贴
    function lowerPaste(handler)
    {
        if(!activeFocusItem)
        {
            // 未选中块元件
            return
        }

        if(activeFocusItem === "BLOCK" && isCanPaste(2))
        {
            console.log("activeFocusItem:", activeFocusItem)
            paste_(2, handler)
        }
    }

    // 撤销
    function undo(handler)
    {
        applySnapshot(handler, false)
    }

    // 反撤销
    function redo(handler)
    {
        applySnapshot(handler, true)
    }

    // 应用快照
    function applySnapshot(handler, direction)
    {
        const snapshot = handler(fileKey, deviceName, direction)
        
        if(snapshot && Object.keys(snapshot).length > 0)
        {
            ldManage.saveFile(fileKey, snapshot.content)
            // 需要重新获取网络中的块元件,防止后端对块元件进行修复导致不一致
            analysisNetWorkData(populateNetWorkConnections(ldManage.getAllInfo(control.fileKey)))
            updateFileFlag(fileKey, true)
        }
    }

    // 删除
    function del()
    {
        if(activeFocusItem === "NETWORK")
        {
            // 删除网络
            ldManage.deleteNetwork(fileKey, netWorkNumber) 
            ldManage.fixLDFileConnections(fileKey)
        }
        else if(activeFocusItem === "BLOCK" && isCanDelete())
        {
            // 删除块元件
            deleteBlock()
        }
        else if(activeFocusItem === "PIN" && currentSelectBlockType === "input" && isAdvance())
        {
            ldManage.componentDeleteConnector(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 多输入
    function multipleInput()
    {
        if(currentSelectBlockType &&
           currentSelectBlockType === "advance" &&
           isAdvance())
        {
            if(!currentSelectBolckData.connectors)
            {
                return
            }

            // 所有引脚
            const connectors = currentSelectBolckData.connectors

            // 最大输入引脚id
            let maxInputPinId = 0

            for(let cIndex = 0; cIndex < connectors.length; cIndex++)
            {
                const connector = connectors[cIndex]

                if(connector.Direction.toLowerCase() === "input" && connector.PinId > maxInputPinId)
                {
                    maxInputPinId = connector.PinId
                }
            }

            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.Number, maxInputPinId)
            ldManage.fixLDFileConnections(fileKey)
        }
        else if(isAdvance())
        {
            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 多输出
    function multipleOutput()
    {
        if(currentSelectBlockType &&
           currentSelectBlockType === "advance" &&
           isAdvanceAndMove())
        {
            if(!currentSelectBolckData.connectors)
            {
                return
            }

            // 所有引脚
            const connectors = currentSelectBolckData.connectors

            // 最小输出引脚id
            let minOutputPinId = 0

            for(let cIndex = 0; cIndex < connectors.length; cIndex++)
            {
                const connector = connectors[cIndex]

                if(connector.Direction.toLowerCase() === "output" && connector.PinId < minOutputPinId)
                {
                    minOutputPinId = connector.PinId
                }
            }

            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.Number, minOutputPinId)
            ldManage.fixLDFileConnections(fileKey)
        }
        else if(isAdvanceAndMove())
        {
            ldManage.InsertConnectorAndVariable(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 置反
    function reverse()
    {
        if(currentSelectBlockType &&
            (currentSelectBlockType === "en" ||
            currentSelectBlockType === "input"))
        {
            if(currentSelectBolckData.DataType.toLowerCase() === "bool")
            {
                ldManage.modifyConnectorNegated(fileKey, currentSelectBolckData.ParentId, currentSelectBolckData.PinId)
                ldManage.fixLDFileConnections(fileKey)
            }
        }
        // 选中的块元件非Function、FunctionBlock、advance、set0、set1才能置反
        else if(currentSelectBlockType &&
            currentSelectBlockType !== "func" && 
            currentSelectBlockType !== "fb" && 
            currentSelectBlockType !== "advance" &&
            currentSelectBlockType !== "set0" &&
            currentSelectBlockType !== "set1")
        {
            ldManage.modifyConnectorNegated(fileKey, currentSelectBolckData.Number, currentSelectBolckData.PinId)
            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 置位/复位
    function setOrReset()
    {
        if(currentSelectBlockType &&
            (currentSelectBlockType === "coil" ||
            currentSelectBlockType === "set1" ||
            currentSelectBlockType === "set0")
            )
        {
            if(currentSelectBlockType === "coil")
            {
                // 置位 set1
                ldManage.setCoilToSet1(fileKey, currentSelectBolckData.Number)
            }
            else if(currentSelectBlockType === "set1")
            {
                // 置位 set0
                ldManage.resetCoilToSet0(fileKey, currentSelectBolckData.Number)
            }
            else if(currentSelectBlockType === "set1" || 
                    currentSelectBlockType === "set0")
            {
                // 复位
                ldManage.resetToCoil(fileKey, currentSelectBolckData.Number)
            }

            ldManage.fixLDFileConnections(fileKey)
        }
    }

    // 添加快照内容
    function addSnapshot(content)
    {
        const snapshot = {
            content,
            deviceName,
            fileKey
        }

        shortcutController.addSnapshot(snapshot)
    }
}